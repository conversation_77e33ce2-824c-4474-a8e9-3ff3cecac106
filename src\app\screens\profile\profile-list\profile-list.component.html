<div class="container-fluid px-0 py-0">
  <div class="mb-3">
    <h1 class="h2 text-dark">Find Associates</h1>
  </div>

  <!-- Search Form -->
  <div class="card shadow mb-5">
    <div class="card-body p-4">
      <!-- Skill Filter Section -->
      <div class="mb-4">
        <div class="d-flex align-items-center gap-2 mb-2">
          <label class="form-label mb-0 fw-medium">Filter by Skills:</label>
        </div>

        <!-- Selected Skills (Bubbles) -->
        <div
          class="selected-skills-container mb-3"
          *ngIf="selectedSkills.length > 0"
        >
          <div class="skill-chips">
            <span *ngFor="let skill of selectedSkills" class="skill-chip">
              {{ skill }}
              <button
                type="button"
                class="skill-chip-remove"
                (click)="removeSkill(skill)"
                aria-label="Remove skill"
              >
                ×
              </button>
            </span>
          </div>
        </div>

        <!-- Skill Input -->
        <div class="position-relative skill-autocomplete">
          <input
            type="text"
            placeholder="Add skills (e.g., M&A, Patent Law, Corporate Finance)"
            class="form-control"
            [(ngModel)]="skillQuery"
            (input)="onSkillInput($event)"
            autocomplete="off"
          />

          <!-- Skill Suggestions Dropdown -->
          <div *ngIf="showSkillSuggestions" class="autocomplete-dropdown">
            <div class="suggestion-header">
              <span class="suggestion-header-text">Available Skills</span>
            </div>
            <div
              *ngFor="let skill of skillSuggestions"
              class="autocomplete-item"
              (mousedown)="selectSkill(skill)"
            >
              <div class="suggestion-content">
                <div class="suggestion-icon">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                    <line x1="8" y1="21" x2="16" y2="21" />
                    <line x1="12" y1="17" x2="12" y2="21" />
                  </svg>
                </div>
                <span class="suggestion-text">{{ skill }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Search -->
      <div class="mb-4 position-relative search-autocomplete">
        <input
          type="text"
          placeholder="Search profiles by name, title, or expertise..."
          class="form-control py-2"
          [(ngModel)]="searchQuery"
          (input)="onSearchInput($event)"
          (keydown)="onSearchKeydown($event)"
          (focus)="onSearchFocus()"
          (blur)="onSearchBlur()"
          autocomplete="off"
        />

        <!-- Profile Suggestions Dropdown -->
        <div *ngIf="showSuggestions" class="autocomplete-dropdown">
          <div class="suggestion-header">
            <span class="suggestion-header-text">Search Suggestions</span>
          </div>
          <div
            *ngFor="let suggestion of searchSuggestions; let i = index"
            class="autocomplete-item profile-suggestion"
            [class.selected]="i === selectedSuggestionIndex"
            (mousedown)="selectSearchSuggestion(suggestion)"
            (mouseenter)="selectedSuggestionIndex = i"
          >
            <div class="suggestion-content">
              <div class="profile-avatar">
                <div class="avatar-circle">
                  {{ suggestion.initials }}
                </div>
              </div>
              <div class="profile-info">
                <div class="profile-name">{{ suggestion.name }}</div>
                <div class="profile-details">
                  <span class="profile-email">{{ suggestion.email }}</span>
                  <span class="profile-separator">|</span>
                  <span class="profile-title">{{ suggestion.title }}</span>
                </div>
              </div>
              <div class="profile-location">
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                  <circle cx="12" cy="10" r="3" />
                </svg>
                <span>{{ suggestion.location }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row g-3 mb-4">
        <div class="col-md-3">
          <ng-select
            class="experience-level-select icon-info-circle"
            [items]="experienceLevelOptions"
            bindLabel="label"
            bindValue="value"
            placeholder="Experience Level"
            [(ngModel)]="experienceLevel"
            [clearable]="false"
            [searchable]="false"
          >
            <ng-template ng-label-tmp let-item="item">
              <div class="d-flex align-items-center gap-2">
                <app-icon
                  name="info-circle"
                  class="-mt-2"
                  [size]="16"
                ></app-icon>
                <span>{{ item.label }}</span>
              </div>
            </ng-template>
            <ng-template ng-option-tmp let-item="item">
              {{ item.label }}
            </ng-template>
          </ng-select>
        </div>

        <div class="col-md-3">
          <ng-select
            class="location-select icon-map-pin"
            [items]="locationOptions"
            bindLabel="label"
            bindValue="value"
            placeholder="Office Location"
            [(ngModel)]="location"
            [clearable]="false"
            [searchable]="false"
          >
            <ng-template ng-label-tmp let-item="item">
              <div class="d-flex align-items-center gap-2">
                <app-icon name="map-pin" [size]="16"></app-icon>
                <span>{{ item.label }}</span>
              </div>
            </ng-template>
            <ng-template ng-option-tmp let-item="item">
              {{ item.label }}
            </ng-template>
          </ng-select>
        </div>

        <div class="col-md-3">
          <ng-select
            class="language-select icon-book"
            [items]="languageOptions"
            bindLabel="label"
            bindValue="value"
            placeholder="Languages"
            [(ngModel)]="language"
            [clearable]="false"
            [searchable]="false"
          >
            <ng-template ng-label-tmp let-item="item">
              <div class="d-flex align-items-center gap-2">
                <app-icon name="book" [size]="16"></app-icon>
                <span>{{ item.label }}</span>
              </div>
            </ng-template>
            <ng-template ng-option-tmp let-item="item">
              {{ item.label }}
            </ng-template>
          </ng-select>
        </div>

        <div class="col-md-3">
          <app-date-picker
            placeholder="Pick availability date"
            [initialSelection]="datePickerInitialSelection"
            (dateSelected)="onDateSelectionChange($event)"
            class="w-100"
          ></app-date-picker>
        </div>
      </div>

      <div class="d-flex gap-2">
        <button class="btn btn-primary px-4 py-2" (click)="searchProfiles()">
          Search
        </button>
        <button
          *ngIf="hasFiltersApplied()"
          class="btn btn-outline-secondary px-4 py-2"
          (click)="clearFilters()"
        >
          Clear
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div
    *ngIf="loading"
    class="d-flex justify-content-center align-items-center py-5"
  >
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger shadow mb-5">
    <div class="d-flex align-items-center">
      <i class="bi bi-exclamation-triangle me-2"></i>
      <span>Error loading profiles. Please try again later.</span>
    </div>
  </div>

  <!-- Toggle Style Button -->
  <!-- <div class="text-center mb-4">
    <div class="btn-group" role="group">
      <button
        class="btn btn-outline-secondary btn-sm"
        [class.active]="profileListStyle === 'style1'"
        (click)="setProfileListStyle('style1')"
      >
        Card Style original
      </button>
      <button
        class="btn btn-outline-secondary btn-sm"
        [class.active]="profileListStyle === 'style2'"
        (click)="setProfileListStyle('style2')"
      >
        Card Style Option Two
      </button>
    </div>
  </div> -->

  <!-- Profile List Style 1 -->
  <div *ngIf="!loading && !error && profileListStyle === 'style1'">
    <!-- Profile Card -->
    <div
      *ngFor="let profile of profiles"
      class="card cursor-pointer hover-shadow transition-shadow mb-3"
      [routerLink]="['/profile', profile.id]"
    >
      <div class="card-body p-3">
        <div class="d-flex justify-content-between align-items-center">
          <div class="d-flex align-items-center gap-3">
            <div
              class="rounded-circle bg-primary bg-opacity-10 text-primary d-flex align-items-center justify-content-center"
              style="
                width: 64px;
                height: 64px;
                font-size: 0.75rem;
                font-weight: 500;
              "
            >
              {{ profile.initials }}
            </div>
            <div>
              <h2 class="h5 mb-1">{{ profile.name }}</h2>
              <div class="d-flex align-items-center gap-1 mb-1">
                <i class="bi bi-briefcase text-muted"></i>
                <span
                  class="text-muted small"
                  *ngIf="
                    profile.practiceAreas && profile.practiceAreas.length > 0
                  "
                >
                  {{ profile.practiceAreas[0].name }}
                </span>
              </div>
              <div class="d-flex align-items-center gap-1">
                <i class="bi bi-geo-alt text-muted"></i>
                <span class="text-muted small">{{ profile.location }}</span>
              </div>
            </div>
          </div>

          <div class="text-end">
            <div>
              <span class="small fw-medium"
                >{{ profile.yearsExperience }} years experience</span
              >
            </div>
            <div class="mt-1">
              <span class="text-success small">Available now</span>
            </div>
            <div class="mt-1">
              <span class="badge-flat badge-flat-success"
                >{{ profile.availability.matchPercentage }}% match</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Profile List Style 2 (Option Two) -->
  <div *ngIf="!loading && !error && profileListStyle === 'style2'">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="h3">Associate Profiles</h2>
      <a href="#" class="text-primary text-decoration-none"
        >See all associates</a
      >
    </div>

    <div class="row g-4">
      <!-- Profile Card -->
      <div *ngFor="let profile of profiles" class="col-md-6 col-lg-4 col-xl-3">
        <div
          class="card shadow-sm h-100 cursor-pointer hover-shadow transition-all"
          [routerLink]="['/profile', profile.id]"
        >
          <div class="card-body p-4 text-center">
            <div class="mb-4">
              <div
                class="rounded-circle bg-light mx-auto mb-3 overflow-hidden border border-2 border-light"
                style="width: 96px; height: 96px"
              >
                <div
                  *ngIf="!profile.avatarUrl"
                  class="w-100 h-100 bg-primary bg-opacity-10 text-primary d-flex align-items-center justify-content-center"
                  style="font-size: 1.25rem; font-weight: 700"
                >
                  {{ profile.initials }}
                </div>
                <img
                  *ngIf="profile.avatarUrl"
                  [src]="profile.avatarUrl"
                  alt="{{ profile.name }}"
                  class="w-100 h-100 object-fit-cover"
                />
              </div>
              <div class="d-flex align-items-center justify-content-center">
                <h3 class="h5 mb-0">
                  {{ profile.name }}
                </h3>
                <i class="bi bi-arrow-right text-primary ms-1"></i>
              </div>
              <div class="mt-2">
                <span
                  class="badge bg-success bg-opacity-10 text-success border border-success border-opacity-25"
                >
                  <span
                    class="rounded-circle bg-success me-1"
                    style="width: 6px; height: 6px; display: inline-block"
                  ></span>
                  {{ profile.availability.status }}
                </span>
              </div>
            </div>

            <div
              class="d-flex justify-content-between align-items-center mb-3 border-top border-bottom py-2"
            >
              <span class="small fw-medium"
                >{{ profile.yearsExperience }} years experience</span
              >
              <span
                class="badge bg-primary bg-opacity-10 text-primary border border-primary border-opacity-25"
                >{{ profile.availability.matchPercentage }}% match</span
              >
            </div>

            <div class="text-muted small mb-3">
              <div
                class="d-flex align-items-center justify-content-between mb-2"
              >
                <div class="d-flex align-items-center gap-2">
                  <i class="bi bi-briefcase text-muted"></i>
                  <p
                    *ngIf="
                      profile.practiceAreas && profile.practiceAreas.length > 0
                    "
                    class="text-truncate mb-0"
                    style="max-width: 120px"
                  >
                    {{ profile.practiceAreas[0].name }}
                  </p>
                </div>
                <div class="d-flex align-items-center gap-2">
                  <i class="bi bi-geo-alt text-muted"></i>
                  <p class="text-truncate mb-0" style="max-width: 100px">
                    {{ profile.location }}
                  </p>
                </div>
              </div>
            </div>

            <div class="text-muted small mb-3">
              <p
                class="mb-0"
                style="
                  display: -webkit-box;
                  -webkit-line-clamp: 3;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                "
              >
                {{ profile.professionalSummary }}
              </p>
            </div>

            <div class="d-flex justify-content-center gap-3 border-top pt-3">
              <a
                href="#"
                class="text-muted text-decoration-none hover-text-primary"
              >
                <i class="bi bi-twitter"></i>
              </a>
              <a
                href="#"
                class="text-muted text-decoration-none hover-text-primary"
              >
                <i class="bi bi-linkedin"></i>
              </a>
              <a
                href="#"
                class="text-muted text-decoration-none hover-text-danger"
              >
                <i class="bi bi-envelope"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
