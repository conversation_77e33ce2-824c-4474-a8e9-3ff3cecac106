import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ProfileService } from '../../../core/services/profile/profile.service';
import { ProfileData } from '../../../core/models/profile.model';
import { IconComponent } from 'src/app/shared/components/icon/icon.component';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  switchMap,
  takeUntil,
} from 'rxjs';

interface SelectOption {
  label: string;
  value: string;
}

@Component({
  selector: 'app-profile-list',
  templateUrl: './profile-list.component.html',
  styleUrls: ['./profile-list.component.css'],
})
export class ProfileListComponent implements OnInit, OnDestroy {
  profiles: ProfileData[] = [];
  loading = true;
  error = false;
  profileListStyle: 'style1' | 'style2' = 'style1';

  // Search filters
  searchQuery = '';
  experienceLevel: string | null = null;
  location: string | null = null;
  language: string | null = null;
  availability: string | null = null;
  availabilityDate: string | null = null;

  // Date picker properties
  datePickerInitialSelection: any = null;

  // Autocomplete properties
  searchSuggestions: any[] = [];
  showSuggestions = false;
  selectedSuggestionIndex = -1;
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Skill filter properties
  selectedSkills: string[] = [];
  skillSuggestions: any[] = [];
  showSkillSuggestions = false;
  skillQuery = '';
  private skillSearchSubject = new Subject<string>();

  // Display limits
  maxSkillsToShow = 3;
  maxProfilesToShow = 5;

  // Infinite scroll properties
  displayedProfiles: ProfileData[] = [];
  allProfiles: ProfileData[] = [];
  profilesPerPage = 10;
  currentPage = 0;
  isLoadingMore = false;
  hasMoreProfiles = true;

  // Options for ng-select dropdowns
  experienceLevelOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'Junior (1-3 years)', value: 'Junior (1-3 years)' },
    { label: 'Mid-level (4-6 years)', value: 'Mid-level (4-6 years)' },
    { label: 'Senior (7-10 years)', value: 'Senior (7-10 years)' },
    { label: 'Partner (10+ years)', value: 'Partner (10+ years)' },
  ];

  locationOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'New York', value: 'New York' },
    { label: 'San Francisco', value: 'San Francisco' },
    { label: 'Chicago', value: 'Chicago' },
    { label: 'Los Angeles', value: 'Los Angeles' },
  ];

  languageOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'English', value: 'English' },
    { label: 'Spanish', value: 'Spanish' },
    { label: 'Mandarin', value: 'Mandarin' },
    { label: 'French', value: 'French' },
  ];

  availabilityOptions: SelectOption[] = [
    { label: 'Any', value: '' },
    { label: 'Available now', value: 'Available now' },
    { label: 'Available next week', value: 'Available next week' },
    { label: 'Available next month', value: 'Available next month' },
  ];

  constructor(private profileService: ProfileService) {}

  ngOnInit(): void {
    this.loadProfiles();
    this.setupAutocomplete();
    this.setupSkillAutocomplete();

    // Initialize date picker if availability is already set
    if (this.availability) {
      const today = new Date();

      switch (this.availability) {
        case 'Available now':
          this.availabilityDate = today.toISOString().split('T')[0];
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: this.availabilityDate,
          };
          break;
        case 'Available next week':
          const nextWeek = new Date();
          nextWeek.setDate(today.getDate() + 7);
          this.availabilityDate = nextWeek.toISOString().split('T')[0];
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: this.availabilityDate,
          };
          break;
        case 'Available next month':
          const nextMonth = new Date();
          nextMonth.setDate(today.getDate() + 30);
          this.availabilityDate = nextMonth.toISOString().split('T')[0];
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: this.availabilityDate,
          };
          break;
        default:
          this.availabilityDate = null;
          this.datePickerInitialSelection = {
            type: 'single',
            singleDate: null,
          };
      }
    } else {
      this.datePickerInitialSelection = {
        type: 'single',
        singleDate: null,
      };
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadProfiles(): void {
    this.profileService.getAllProfiles().subscribe({
      next: (data) => {
        this.profiles = data;
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading profiles', err);
        this.error = true;
        this.loading = false;
      },
    });
  }

  searchProfiles(): void {
    this.loading = true;
    this.error = false;

    this.profileService
      .searchProfiles(
        this.searchQuery,
        this.experienceLevel || '',
        this.location || '',
        this.language || '',
        this.availability || ''
      )
      .subscribe({
        next: (data) => {
          // Filter by selected skills if any
          let filteredData = data;
          if (this.selectedSkills.length > 0) {
            filteredData = data.filter((profile) => {
              const hasSkill = this.selectedSkills.some((selectedSkill) => {
                const skillLower = selectedSkill.toLowerCase();

                // Check skills
                const skillMatch = profile.skills.some(
                  (skill) =>
                    skill.name.toLowerCase().includes(skillLower) ||
                    skillLower.includes(skill.name.toLowerCase())
                );

                // Check practice areas
                const practiceMatch = profile.practiceAreas.some(
                  (area) =>
                    area.name.toLowerCase().includes(skillLower) ||
                    skillLower.includes(area.name.toLowerCase())
                );

                // Check for common M&A variations
                if (
                  skillLower.includes('merger') ||
                  skillLower.includes('acquisition')
                ) {
                  const maMatch =
                    profile.skills.some(
                      (skill) =>
                        skill.name.toLowerCase().includes('m&a') ||
                        skill.name.toLowerCase().includes('merger') ||
                        skill.name.toLowerCase().includes('acquisition')
                    ) ||
                    profile.practiceAreas.some(
                      (area) =>
                        area.name.toLowerCase().includes('m&a') ||
                        area.name.toLowerCase().includes('merger') ||
                        area.name.toLowerCase().includes('acquisition')
                    );
                  return skillMatch || practiceMatch || maMatch;
                }

                return skillMatch || practiceMatch;
              });

              return hasSkill;
            });
          }

          // Store all profiles and initialize pagination
          this.allProfiles = filteredData;
          this.currentPage = 0;
          this.hasMoreProfiles = this.allProfiles.length > this.profilesPerPage;
          this.loadInitialProfiles();
          this.loading = false;
        },
        error: (err) => {
          console.error('Error searching profiles', err);
          this.error = true;
          this.loading = false;
        },
      });
  }

  // The setter methods are no longer needed as we're using two-way binding with [(ngModel)]

  setProfileListStyle(style: 'style1' | 'style2'): void {
    this.profileListStyle = style;
  }

  clearFilters(): void {
    // Reset all search filters to default values
    this.searchQuery = '';
    this.experienceLevel = null;
    this.location = null;
    this.language = null;
    this.availability = null;
    this.availabilityDate = null;
    this.selectedSkills = [];
    this.skillQuery = '';

    // Reset date picker
    this.datePickerInitialSelection = {
      type: 'single',
      singleDate: null,
    };

    // Load all profiles (unfiltered)
    this.loadProfiles();
  }

  hasFiltersApplied(): boolean {
    // Check if any search filters are applied
    return !!(
      this.searchQuery ||
      this.experienceLevel ||
      this.location ||
      this.language ||
      this.availability ||
      this.availabilityDate ||
      this.selectedSkills.length > 0
    );
  }

  onDateSelectionChange(selection: any): void {
    if (selection.type === 'single' && selection.singleDate) {
      this.availabilityDate = selection.singleDate;

      const date = new Date(selection.singleDate);
      const today = new Date();
      const nextWeek = new Date();
      nextWeek.setDate(today.getDate() + 7);
      const nextMonth = new Date();
      nextMonth.setDate(today.getDate() + 30);

      // Set availability based on selected date
      if (date <= today) {
        this.availability = 'Available now';
      } else if (date <= nextWeek) {
        this.availability = 'Available next week';
      } else if (date <= nextMonth) {
        this.availability = 'Available next month';
      } else {
        this.availability = ''; // Any
      }
    } else if (
      selection.type === 'range' &&
      selection.startDate &&
      selection.endDate
    ) {
      // For date range, we'll use the start date to determine availability
      this.availabilityDate = selection.startDate;

      const date = new Date(selection.startDate);
      const today = new Date();
      const nextWeek = new Date();
      nextWeek.setDate(today.getDate() + 7);
      const nextMonth = new Date();
      nextMonth.setDate(today.getDate() + 30);

      // Set availability based on selected date
      if (date <= today) {
        this.availability = 'Available now';
      } else if (date <= nextWeek) {
        this.availability = 'Available next week';
      } else if (date <= nextMonth) {
        this.availability = 'Available next month';
      } else {
        this.availability = ''; // Any
      }
    } else {
      this.availabilityDate = null;
      this.availability = null;
    }
  }

  // Autocomplete methods
  setupAutocomplete(): void {
    this.searchSubject
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((query) => this.profileService.getSearchSuggestions(query)),
        takeUntil(this.destroy$)
      )
      .subscribe((suggestions) => {
        this.searchSuggestions = suggestions;
        this.showSuggestions = suggestions.length > 0;
        this.selectedSuggestionIndex = -1;
      });
  }

  setupSkillAutocomplete(): void {
    this.skillSearchSubject
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((query) => this.profileService.getSkillSuggestions(query)),
        takeUntil(this.destroy$)
      )
      .subscribe((suggestions) => {
        this.skillSuggestions = suggestions.filter(
          (skill) => !this.selectedSkills.includes(skill.name)
        );
        this.showSkillSuggestions = this.skillSuggestions.length > 0;
      });
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const query = target.value;
    this.searchQuery = query;

    if (query.length > 0) {
      // Show both profile and skill suggestions
      this.searchSubject.next(query);
      this.skillSearchSubject.next(query);
    } else {
      this.hideSuggestions();
      this.hideSkillSuggestions();
    }
  }

  onSearchKeydown(event: KeyboardEvent): void {
    if (!this.showSuggestions && !this.showSkillSuggestions) return;

    const totalSuggestions =
      this.skillSuggestions.length + this.searchSuggestions.length;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.selectedSuggestionIndex = Math.min(
          this.selectedSuggestionIndex + 1,
          totalSuggestions - 1
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.selectedSuggestionIndex = Math.max(
          this.selectedSuggestionIndex - 1,
          -1
        );
        break;
      case 'Enter':
        event.preventDefault();
        if (this.selectedSuggestionIndex >= 0) {
          // Check if selection is in skills section
          if (this.selectedSuggestionIndex < this.skillSuggestions.length) {
            this.selectSkill(
              this.skillSuggestions[this.selectedSuggestionIndex]
            );
          } else {
            // Selection is in associates section
            const associateIndex =
              this.selectedSuggestionIndex - this.skillSuggestions.length;
            this.selectSearchSuggestion(this.searchSuggestions[associateIndex]);
          }
        } else {
          this.hideSuggestions();
          this.hideSkillSuggestions();
          this.searchProfiles();
        }
        break;
      case 'Escape':
        event.preventDefault();
        this.hideSuggestions();
        this.hideSkillSuggestions();
        break;
    }
  }

  selectSearchSuggestion(suggestion: any): void {
    if (suggestion.type === 'profile') {
      // Navigate to profile
      window.location.href = `/profile/${suggestion.profileId}`;
    }
    this.hideSuggestions();
  }

  // Skill filter methods
  onSkillInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const query = target.value;
    this.skillQuery = query;

    if (query.length > 0) {
      this.skillSearchSubject.next(query);
    } else {
      this.hideSkillSuggestions();
    }
  }

  selectSkill(skill: any): void {
    const skillName = typeof skill === 'string' ? skill : skill.name;
    if (!this.selectedSkills.includes(skillName)) {
      this.selectedSkills.push(skillName);
      this.searchQuery = skillName; // Keep the selected skill in search input
      this.hideSkillSuggestions();
      this.hideSuggestions();
      this.searchProfiles();
    }
  }

  removeSkill(skill: string): void {
    this.selectedSkills = this.selectedSkills.filter((s) => s !== skill);

    // Update search query - if removing the current search query skill, clear it
    if (this.searchQuery === skill) {
      this.searchQuery = '';
    }

    this.searchProfiles();
  }

  hideSuggestions(): void {
    this.showSuggestions = false;
    this.selectedSuggestionIndex = -1;
  }

  hideSkillSuggestions(): void {
    this.showSkillSuggestions = false;
  }

  // Display limit methods
  get displayedSkillSuggestions(): any[] {
    return this.skillSuggestions.slice(0, this.maxSkillsToShow);
  }

  get displayedSearchSuggestions(): any[] {
    return this.searchSuggestions.slice(0, this.maxProfilesToShow);
  }

  viewAllProfiles(): void {
    // Close suggestions dropdown
    this.hideSuggestions();
    this.hideSkillSuggestions();

    // Clear search query to show all profiles
    this.searchQuery = '';
    this.selectedSkills = [];

    // Load all profiles
    this.searchProfiles();
  }

  // Infinite scroll methods
  loadInitialProfiles(): void {
    this.displayedProfiles = this.allProfiles.slice(0, this.profilesPerPage);
    this.currentPage = 1;
    this.hasMoreProfiles = this.allProfiles.length > this.profilesPerPage;
  }

  loadMoreProfiles(): void {
    if (this.isLoadingMore || !this.hasMoreProfiles) {
      return;
    }

    this.isLoadingMore = true;

    // Simulate loading delay
    setTimeout(() => {
      const startIndex = this.currentPage * this.profilesPerPage;
      const endIndex = startIndex + this.profilesPerPage;
      const nextProfiles = this.allProfiles.slice(startIndex, endIndex);

      this.displayedProfiles = [...this.displayedProfiles, ...nextProfiles];
      this.currentPage++;
      this.hasMoreProfiles = endIndex < this.allProfiles.length;
      this.isLoadingMore = false;
    }, 500);
  }

  onScroll(): void {
    const element = document.documentElement;
    const atBottom =
      element.scrollHeight - element.scrollTop <= element.clientHeight + 100;

    if (atBottom && this.hasMoreProfiles && !this.isLoadingMore) {
      this.loadMoreProfiles();
    }
  }

  onSearchBlur(): void {
    // Delay hiding suggestions to allow for click events
    setTimeout(() => {
      this.hideSuggestions();
      this.hideSkillSuggestions();
    }, 200);
  }

  onSearchFocus(): void {
    if (this.searchQuery.length > 0) {
      if (this.searchSuggestions.length > 0) {
        this.showSuggestions = true;
      }
      if (this.skillSuggestions.length > 0) {
        this.showSkillSuggestions = true;
      }
    }
  }
}
