/* Common styles for all ng-select placeholders */
.icon-info-circle ::ng-deep .ng-placeholder,
.icon-map-pin ::ng-deep .ng-placeholder,
.icon-book ::ng-deep .ng-placeholder {
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  padding-left: 24px !important; /* Make space for the icon */
}

/* Hide placeholder when an option is selected */
.icon-info-circle ::ng-deep .ng-has-value .ng-placeholder,
.icon-map-pin ::ng-deep .ng-has-value .ng-placeholder,
.icon-book ::ng-deep .ng-has-value .ng-placeholder {
  display: none !important;
}

/* Date picker container styles */
.date-picker-container {
  position: relative;
  width: 100%;
}

.date-picker-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  color: var(--content3);
  pointer-events: none;
}

.date-picker {
  padding-left: 36px !important;
  color: var(--content1);
  background-color: var(--backgroundPrimary);
}

/* Style the date picker calendar icon */
.date-picker::-webkit-calendar-picker-indicator {
  opacity: 0.6;
  cursor: pointer;
}

/* Style the date picker placeholder */
.date-picker::placeholder {
  color: var(--content3);
}

/* Skill Filter Styles */
.selected-skills-container {
  margin-bottom: 1rem;
}

.skill-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-chip {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: #3b82f6;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  border: none;
}

.skill-chip-remove {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  line-height: 1;
  cursor: pointer;
  padding: 0;
  margin-left: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.15s ease;
}

.skill-chip-remove:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Suggestions Dialog Styles */
.search-autocomplete {
  position: relative;
}

.suggestions-dialog {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 8px;
}

.suggestions-section {
  border-bottom: 1px solid #f1f5f9;
}

.suggestions-section:last-child {
  border-bottom: none;
}

.section-header {
  padding: 12px 20px 8px 20px;
  background-color: #fafbfc;
  border-bottom: 1px solid #f1f5f9;
}

.section-title {
  font-size: 11px;
  font-weight: 600;
  color: #64748b;
  letter-spacing: 0.5px;
}

.suggestions-list {
  /* Remove individual scrollbars - parent handles scrolling */
}

.suggestion-header {
  padding: 8px 16px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.autocomplete-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.15s ease;
}

.autocomplete-item:last-child {
  border-bottom: none;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
  background-color: #f8fafc;
}

.suggestion-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.suggestion-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #64748b;
  flex-shrink: 0;
}

.suggestion-text {
  color: #334155;
  font-size: 14px;
  font-weight: 500;
}

.autocomplete-item.selected .suggestion-text {
  color: #1e40af;
}

.autocomplete-item.selected .suggestion-icon {
  color: #1e40af;
}

/* New Suggestion Item Styles */
.suggestion-item {
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.suggestion-item:hover {
  background-color: #f8fafc;
}

.suggestion-item.selected {
  background-color: #eff6ff;
}

/* Skill Item Styles */
.skill-item {
  justify-content: space-between;
}

.skill-icon {
  width: 40px;
  height: 40px;
  background-color: #8b5cf6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.skill-info {
  flex: 1;
  min-width: 0;
}

.skill-name {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.skill-count {
  font-size: 13px;
  color: #6b7280;
}

.skill-category {
  background-color: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

/* Associate Item Styles */
.associate-item {
  align-items: flex-start;
  padding: 16px 20px;
}

.associate-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e5e7eb;
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.associate-info {
  flex: 1;
  min-width: 0;
}

.associate-name {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.associate-details {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.associate-details svg {
  color: #9ca3af;
  flex-shrink: 0;
}

.associate-email {
  color: #6b7280;
}

.associate-separator {
  color: #d1d5db;
  margin: 0 2px;
}

.associate-title {
  color: #6b7280;
}

.associate-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
  flex-shrink: 0;
  margin-top: 4px;
}

.associate-location svg {
  color: #9ca3af;
}

.associate-item.selected .associate-name {
  color: #1e40af;
}

.associate-item.selected .avatar-circle {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Show More Item Styles */
.show-more-item {
  border-top: 1px solid #f1f5f9;
  background-color: #fafbfc;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.show-more-item:hover {
  background-color: #f1f5f9;
}

.show-more-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.show-more-text {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
}

.show-more-icon {
  color: #9ca3af;
  transition: transform 0.15s ease;
}

.show-more-item:hover .show-more-icon {
  color: #6b7280;
}

/* Profile Suggestion Styles */
.profile-suggestion {
  padding: 16px;
}

.profile-suggestion .suggestion-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.profile-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 15px;
  margin-bottom: 2px;
}

.profile-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #6b7280;
}

.profile-email {
  color: #6b7280;
}

.profile-separator {
  color: #d1d5db;
}

.profile-title {
  color: #6b7280;
}

.profile-location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #9ca3af;
  flex-shrink: 0;
}

.profile-location svg {
  color: #9ca3af;
}

.profile-suggestion.selected .profile-name {
  color: #1e40af;
}

.profile-suggestion.selected .avatar-circle {
  background-color: #1e40af;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .skill-chip {
    background-color: #1e40af;
  }

  .suggestions-dialog {
    background: #1e293b;
    border-color: #334155;
  }

  .section-header {
    background-color: #334155;
    border-bottom-color: #475569;
  }

  .section-title {
    color: #94a3b8;
  }

  .suggestions-section {
    border-bottom-color: #334155;
  }

  .suggestion-header {
    background-color: #334155;
    border-bottom-color: #475569;
    color: #94a3b8;
  }

  .autocomplete-item {
    border-bottom-color: #334155;
  }

  .autocomplete-item:hover,
  .autocomplete-item.selected {
    background-color: #334155;
  }

  .suggestion-text {
    color: #e2e8f0;
  }

  .suggestion-icon {
    color: #94a3b8;
  }

  .autocomplete-item.selected .suggestion-text {
    color: #60a5fa;
  }

  .autocomplete-item.selected .suggestion-icon {
    color: #60a5fa;
  }

  .profile-name {
    color: #e2e8f0;
  }

  .profile-details {
    color: #94a3b8;
  }

  .profile-email,
  .profile-title {
    color: #94a3b8;
  }

  .profile-separator {
    color: #64748b;
  }

  .profile-location {
    color: #6b7280;
  }

  .profile-suggestion.selected .profile-name {
    color: #60a5fa;
  }

  .avatar-circle {
    background-color: #1e40af;
  }

  .profile-suggestion.selected .avatar-circle {
    background-color: #60a5fa;
  }

  /* Dark mode for new suggestion items */
  .suggestion-item:hover {
    background-color: #334155;
  }

  .suggestion-item.selected {
    background-color: #1e3a8a;
  }

  .skill-name,
  .associate-name {
    color: #e2e8f0;
  }

  .skill-count,
  .associate-details,
  .associate-email,
  .associate-title {
    color: #94a3b8;
  }

  .skill-category {
    background-color: #374151;
    color: #9ca3af;
  }

  .associate-separator {
    color: #64748b;
  }

  .associate-location {
    color: #6b7280;
  }

  .associate-item.selected .associate-name {
    color: #60a5fa;
  }

  .associate-item.selected .avatar-circle {
    background-color: #1e3a8a;
    color: #60a5fa;
  }

  /* Dark mode for show more items */
  .show-more-item {
    background-color: #334155;
    border-top-color: #475569;
  }

  .show-more-item:hover {
    background-color: #475569;
  }

  .show-more-text {
    color: #94a3b8;
  }

  .show-more-icon {
    color: #64748b;
  }

  .show-more-item:hover .show-more-icon {
    color: #94a3b8;
  }
}

/* Add icon using ::before pseudo-element with SVG background images */
.icon-info-circle ::ng-deep .ng-placeholder::before,
.icon-map-pin ::ng-deep .ng-placeholder::before,
.icon-book ::ng-deep .ng-placeholder::before,
.icon-calendar ::ng-deep .ng-placeholder::before {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  display: block;
}

/* Individual icon styles using SVG data URLs for Lucide icons */
.icon-info-circle ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='M12 16v-4'/%3E%3Cpath d='M12 8h.01'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-map-pin ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z'/%3E%3Ccircle cx='12' cy='10' r='3'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-book ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.icon-calendar ::ng-deep .ng-placeholder::before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 2v3'/%3E%3Cpath d='M16 2v3'/%3E%3Cpath d='M3 10h18'/%3E%3Cpath d='M3 5h18v16H3z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}
