import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  ProfileData,
  PracticeArea,
  Skill,
  IndustryExperience,
  NotableMatter,
} from '../../models/profile.model';

@Injectable({
  providedIn: 'root',
})
export class ProfileService {
  private profiles: ProfileData[] = [
    {
      id: 1,
      name: '<PERSON>',
      initials: 'S<PERSON>',
      title: 'Senior Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'New York',
      yearsExperience: 5,
      yearsWithFirm: 5,
      practiceAreas: [
        { name: 'Corporate Law', level: 'Expert' },
        { name: 'Mergers & Acquisitions', level: 'Advanced' },
        { name: 'Mergers & Acquisitions', level: 'Intermediate' },
        { name: 'Mergers & Acquisitions', level: 'Beginner' },
      ],
      skills: [
        { name: 'Private Equity' },
        { name: 'Venture Capital' },
        { name: 'Public M&A' },
        { name: 'Cross-border M&A' },
        { name: 'Joint Ventures' },
        { name: 'Corporate Restructuring' },
        { name: 'IPOs' },
        { name: 'Debt Financing' },
        { name: 'Asset Sales' },
        { name: 'Stock Purchase' },
        { name: 'Due Diligence' },
        { name: 'Deal Structuring' },
        { name: 'Strategic Alliances' },
        { name: 'Corporate Governance' },
        { name: 'Securities Regulation' },
        { name: 'Contract Negotiation' },
        { name: 'Regulatory Compliance' },
        { name: 'International Trade' },
        { name: 'Technology Licensing' },
        { name: 'Intellectual Property' },
        { name: 'Data Privacy' },
        { name: 'Employment Law' },
        { name: 'Environmental Law' },
        { name: 'Real Estate' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Healthcare' },
        { name: 'Financial Services' },
        { name: 'Manufacturing' },
        { name: 'Retail' },
        { name: 'Energy' },
        { name: 'Transportation' },
        { name: 'Media & Entertainment' },
        { name: 'Telecommunications' },
        { name: 'Real Estate' },
        { name: 'Consumer Goods' },
        { name: 'Pharmaceuticals' },
      ],
      professionalSummary:
        'Corporate attorney with extensive experience in M&A transactions and private equity deals. Specialized in technology and healthcare sectors.',
      education: [
        {
          degree: 'J.D.',
          institution: 'Harvard Law School',
        },
        {
          degree: 'B.A.',
          institution: 'Yale University',
        },
      ],
      barAdmissions: [{ state: 'New York' }, { state: 'California' }],
      notableMatters: [
        {
          title: 'Cross-border acquisition of tech startup',
          description: 'Led the legal team in $500M acquisition deal',
          year: '2024',
          role: 'Lead Associate',
        },
        {
          title: 'Major healthcare merger',
          description: 'Managed due diligence and regulatory compliance',
          year: '2023',
          role: 'Senior Associate',
        },
        {
          title: 'International joint venture',
          description: 'Structured and negotiated $1B joint venture agreement',
          year: '2023',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available now',
        matchPercentage: 95,
      },
    },
    {
      id: 2,
      name: 'Michael Chen',
      initials: 'MC',
      title: 'Associate',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'San Francisco',
      yearsExperience: 7,
      yearsWithFirm: 4,
      practiceAreas: [
        { name: 'Intellectual Property', level: 'Expert' },
        { name: 'Technology Transactions', level: 'Advanced' },
      ],
      skills: [
        { name: 'Patent Prosecution' },
        { name: 'Patent Litigation' },
        { name: 'Trademark Registration' },
        { name: 'Copyright Law' },
        { name: 'IP Due Diligence' },
        { name: 'Technology Licensing' },
        { name: 'Software Licensing' },
        { name: 'Open Source Compliance' },
        { name: 'Data Privacy' },
        { name: 'Trade Secrets' },
        { name: 'IP Portfolio Management' },
        { name: 'IP Strategy' },
      ],
      industryExperience: [
        { name: 'Technology' },
        { name: 'Software' },
        { name: 'Biotechnology' },
        { name: 'Pharmaceuticals' },
        { name: 'Medical Devices' },
        { name: 'Telecommunications' },
        { name: 'Consumer Electronics' },
      ],
      professionalSummary:
        'Intellectual property attorney specializing in technology and life sciences. Experienced in patent prosecution, IP litigation, and technology transactions.',
      education: [
        {
          degree: 'J.D.',
          institution: 'Stanford Law School',
        },
        {
          degree: 'B.S. Computer Science',
          institution: 'MIT',
        },
      ],
      barAdmissions: [
        { state: 'California' },
        { state: 'New York' },
        { state: 'USPTO' },
      ],
      notableMatters: [
        {
          title: 'Patent litigation for tech giant',
          description:
            'Successfully defended client in $200M patent infringement case',
          year: '2023',
          role: 'Lead Associate',
        },
        {
          title: 'IP due diligence for acquisition',
          description:
            'Conducted comprehensive IP review for $1.5B acquisition',
          year: '2022',
          role: 'Associate',
        },
        {
          title: 'Global licensing program',
          description:
            'Developed and implemented global technology licensing strategy',
          year: '2021',
          role: 'Associate',
        },
      ],
      availability: {
        status: 'Available next week',
        matchPercentage: 88,
      },
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      initials: 'ER',
      title: 'Partner',
      email: '<EMAIL>',
      phone: '(*************',
      location: 'Chicago',
      yearsExperience: 12,
      yearsWithFirm: 8,
      practiceAreas: [
        { name: 'Litigation', level: 'Expert' },
        { name: 'Dispute Resolution', level: 'Expert' },
        { name: 'Regulatory Investigations', level: 'Advanced' },
      ],
      skills: [
        { name: 'Commercial Litigation' },
        { name: 'Class Action Defense' },
        { name: 'Securities Litigation' },
        { name: 'Antitrust Litigation' },
        { name: 'White Collar Defense' },
        { name: 'Internal Investigations' },
        { name: 'Regulatory Enforcement' },
        { name: 'Arbitration' },
        { name: 'Mediation' },
        { name: 'Trial Advocacy' },
        { name: 'Appellate Practice' },
      ],
      industryExperience: [
        { name: 'Financial Services' },
        { name: 'Banking' },
        { name: 'Insurance' },
        { name: 'Healthcare' },
        { name: 'Pharmaceuticals' },
        { name: 'Energy' },
        { name: 'Technology' },
      ],
      professionalSummary:
        'Experienced litigator with a focus on complex commercial disputes, securities litigation, and regulatory investigations. Represents clients in high-stakes matters across various industries.',
      education: [
        {
          degree: 'J.D.',
          institution: 'Columbia Law School',
        },
        {
          degree: 'B.A.',
          institution: 'University of Chicago',
        },
      ],
      barAdmissions: [
        { state: 'Illinois' },
        { state: 'New York' },
        { state: 'California' },
      ],
      notableMatters: [
        {
          title: 'Securities class action defense',
          description:
            'Successfully defended financial institution in $500M class action',
          year: '2023',
          role: 'Lead Partner',
        },
        {
          title: 'Regulatory investigation',
          description:
            'Represented healthcare company in multi-agency investigation',
          year: '2022',
          role: 'Partner',
        },
        {
          title: 'Commercial arbitration',
          description: 'Won $300M arbitration award for energy company client',
          year: '2021',
          role: 'Partner',
        },
      ],
      availability: {
        status: 'Available next month',
        matchPercentage: 75,
      },
    },
  ];

  constructor() {}

  getAllProfiles(): Observable<ProfileData[]> {
    return of(this.profiles);
  }

  getProfileById(id: number): Observable<ProfileData | undefined> {
    return of(this.profiles.find((profile) => profile.id === +id));
  }

  searchProfiles(
    query: string = '',
    experienceLevel?: string,
    location?: string,
    language?: string,
    availability?: string
  ): Observable<ProfileData[]> {
    let filteredProfiles = this.profiles;

    // Filter by search query
    if (query) {
      const lowerQuery = query.toLowerCase();
      filteredProfiles = filteredProfiles.filter(
        (profile) =>
          profile.name.toLowerCase().includes(lowerQuery) ||
          profile.title.toLowerCase().includes(lowerQuery) ||
          profile.practiceAreas.some((area) =>
            area.name.toLowerCase().includes(lowerQuery)
          ) ||
          profile.skills.some((skill) =>
            skill.name.toLowerCase().includes(lowerQuery)
          )
      );
    }

    // Filter by experience level
    if (experienceLevel) {
      filteredProfiles = filteredProfiles.filter((profile) => {
        if (experienceLevel === 'Junior (1-3 years)') {
          return profile.yearsExperience >= 1 && profile.yearsExperience <= 3;
        } else if (experienceLevel === 'Mid-level (4-6 years)') {
          return profile.yearsExperience >= 4 && profile.yearsExperience <= 6;
        } else if (experienceLevel === 'Senior (7-10 years)') {
          return profile.yearsExperience >= 7 && profile.yearsExperience <= 10;
        } else if (experienceLevel === 'Partner (10+ years)') {
          return profile.yearsExperience > 10;
        }
        return true;
      });
    }

    // Filter by location
    if (location) {
      filteredProfiles = filteredProfiles.filter((profile) =>
        profile.location.includes(location)
      );
    }

    // Filter by availability
    if (availability) {
      filteredProfiles = filteredProfiles.filter(
        (profile) => profile.availability.status === availability
      );
    }

    return of(filteredProfiles);
  }

  getSkillSuggestions(query: string): Observable<string[]> {
    if (!query || query.length < 1) {
      return of([]);
    }

    // Collect all unique skills from all profiles
    const allSkills = new Set<string>();
    this.profiles.forEach((profile) => {
      profile.skills.forEach((skill) => {
        allSkills.add(skill.name);
      });
      profile.practiceAreas.forEach((area) => {
        allSkills.add(area.name);
      });
    });

    // Convert to array and filter based on query
    return of(Array.from(allSkills)).pipe(
      map(
        (skills) =>
          skills
            .filter((skill) =>
              skill.toLowerCase().includes(query.toLowerCase())
            )
            .slice(0, 10) // Limit to 10 suggestions
      )
    );
  }
}
